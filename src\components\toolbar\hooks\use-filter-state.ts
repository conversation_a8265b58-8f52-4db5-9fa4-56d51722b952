import * as React from "react"
import { parseAsStringEnum, useQueryState } from "nuqs"

import { generateId } from "@/lib/id"
import { useDebouncedCallback } from "@/hooks/use-debounced-callback"

import { FilterContext } from "../contexts"
import { getDefaultFilterOperator, getFiltersStateParser } from "../lib"
import { FilterState } from "../types"
import { useToolbar } from "./use-toolbar"

/**
 * @private
 * @internal
 */
export function useFilterState() {
  const context = React.useContext(FilterContext)
  if (!context) throw new Error("Error From useFilterState")

  const { dictionaries, dir, startTransition } = useToolbar()
  const { items, shallow, debounceMs } = context

  const [filters, setFilters] = useQueryState(
    "filters",
    getFiltersStateParser(items.map((item) => item.accessorKey))
      .withDefault([])
      .withOptions({
        startTransition,
        clearOnDefault: true,
        throttleMs: debounceMs,
        shallow,
      })
  )

  const [joinOperator, setJoinOperator] = useQueryState(
    "join",
    parseAsStringEnum(["AND", "OR"]).withDefault("AND").withOptions({
      startTransition,
      clearOnDefault: true,
      shallow: false,
    })
  )

  const addFilter = React.useCallback(() => {
    const item = items[0]
    if (!item) return
    setFilters((prev) => [
      ...prev,
      {
        accessorKey: item.accessorKey,
        value: "",
        variant: item.variant,
        operator: getDefaultFilterOperator(item.variant),
        filterId: generateId({ length: 8 }),
      } as FilterState,
    ])
  }, [items, setFilters])

  const debouncedSetFilters = useDebouncedCallback(setFilters, debounceMs)

  const updateFilter = React.useCallback(
    (filterId: string, updates: Partial<Omit<FilterState, "filterId">>) => {
      debouncedSetFilters(
        (prevFilters) =>
          prevFilters.map((filter) => {
            if (filter.filterId === filterId) {
              return { ...filter, ...updates }
            }
            return filter
          }) as FilterState[]
      )
    },
    [debouncedSetFilters]
  )

  const removeFilter: (filterId: string) => void = React.useCallback(
    (filterId) => {
      setFilters((prevFilters) =>
        prevFilters.filter((filter) => filter.filterId !== filterId)
      )
    },
    [setFilters]
  )

  const resetFilters = React.useCallback(() => {
    setJoinOperator("AND")
    setFilters([])
  }, [setFilters, setJoinOperator])

  return {
    dir,
    items,
    filters,
    addFilter,
    setFilters,
    joinOperator,
    setJoinOperator,
    updateFilter,
    removeFilter,
    resetFilters,
    dictionaries: dictionaries.filter,
  }
}

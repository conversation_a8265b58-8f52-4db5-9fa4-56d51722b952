import * as React from "react"
import { endOfDay, startOfDay } from "date-fns"
import { createParser } from "nuqs/server"
import { z } from "zod/v4"

import {
  FilterJoinState,
  FilterState,
  FilterVariant,
  ToolbarDictionaries,
} from "../types"

const textOperators = [
  "iLike",
  "notILike",
  "eq",
  "ne",
  "isEmpty",
  "isNotEmpty",
] as const
const numericOperators = [
  "eq",
  "ne",
  "lt",
  "lte",
  "gt",
  "gte",
  "isBetween",
  "isEmpty",
  "isNotEmpty",
] as const
const dateOperators = [
  "eq",
  "ne",
  "lt",
  "lte",
  "gt",
  "gte",
  "isBetween",
  "isEmpty",
  "isNotEmpty",
] as const
const multiSelectOperators = [
  "inArray",
  "notInArray",
  "isEmpty",
  "isNotEmpty",
] as const
const selectOperators = ["eq", "ne", "isEmpty", "isNotEmpty"] as const
const booleanOperators = ["eq", "ne"] as const

export const getDefaultFilterOperator = (filterVariant: FilterVariant) => {
  switch (filterVariant) {
    case "text":
      return "iLike"
    case "number":
      return "eq"
    case "date":
      return "eq"
    case "boolean":
      return "eq"
    case "select":
      return "eq"
    case "multiSelect":
      return "inArray"
    default:
      return "eq"
  }
}

export const getFilterOperators = (
  filterVariant: FilterVariant,
  dict: ToolbarDictionaries["filter"]["filterItem"]["operatorSelector"]["operators"]
) => {
  switch (filterVariant) {
    case "text":
      return textOperators.map((operator) => ({
        value: operator,
        label: dict.text[operator],
      }))
    case "number":
    case "numberRange":
      return numericOperators.map((operator) => ({
        value: operator,
        label: dict.number[operator],
      }))
    case "date":
    case "dateRange":
      return dateOperators.map((operator) => ({
        value: operator,
        label: dict.date[operator],
      }))
    case "boolean":
      return booleanOperators.map((operator) => ({
        value: operator,
        label: dict.boolean[operator],
      }))
    case "select":
      return selectOperators.map((operator) => ({
        value: operator,
        label: dict.select[operator],
      }))
    case "multiSelect":
      return multiSelectOperators.map((operator) => ({
        value: operator,
        label: dict.multiSelect[operator],
      }))
  }
}

const filterTextStateSchema = z.object({
  accessorKey: z.string(),
  value: z.string().min(1),
  variant: z.enum(["text"]), // text
  operator: z.enum(textOperators),
  filterId: z.string(),
})

const filterNumericStateSchema = filterTextStateSchema.extend({
  variant: z.enum(["number"]), // number
  operator: z.enum(numericOperators).exclude(["isBetween"]),
  value: z.string().min(1),
})

const filterNumericRangeStateSchema = filterTextStateSchema.extend({
  variant: z.enum(["numberRange"]), // numberRange
  operator: z.enum(["isBetween"]),
  value: z.array(z.string()).min(2),
})

const filterDateStateSchema = filterTextStateSchema.extend({
  variant: z.enum(["date"]), // date
  operator: z.enum(dateOperators).exclude(["isBetween"]),
  value: z.string().min(1),
})

const filterDateRangeStateSchema = filterTextStateSchema.extend({
  variant: z.enum(["dateRange"]), // dateRange
  operator: z.enum(["isBetween"]),
  value: z.array(z.string()).min(2),
})

const filterBooleanStateSchema = filterTextStateSchema.extend({
  variant: z.enum(["boolean"]), // boolean
  operator: z.enum(booleanOperators),
  value: z.stringbool(),
})

const filterSelectStateSchema = filterTextStateSchema.extend({
  variant: z.enum(["select"]), // select
  operator: z.enum(selectOperators),
  value: z.string().min(1),
})

const filterMultiSelectStateSchema = filterTextStateSchema.extend({
  variant: z.enum(["multiSelect"]), // multiSelect
  operator: z.enum(multiSelectOperators),
  value: z.union([z.array(z.string()).min(1), z.enum(["empty", "notEmpty"])]),
})

export const filterJoinStateSchema = z.enum(["AND", "OR"])

export const filterStateSchema = z.discriminatedUnion("variant", [
  filterTextStateSchema,
  filterNumericStateSchema,
  filterNumericRangeStateSchema,
  filterDateStateSchema,
  filterDateRangeStateSchema,
  filterBooleanStateSchema,
  filterSelectStateSchema,
  filterMultiSelectStateSchema,
])

//
// --------------------------------------------------------------------------------------
// Filter Parser
// --------------------------------------------------------------------------------------
//

export const getFiltersStateParser = (itemIds?: string[] | Set<string>) => {
  const validKeys = itemIds
    ? itemIds instanceof Set
      ? itemIds
      : new Set(itemIds)
    : null

  return createParser({
    parse: (value) => {
      try {
        const parsed = JSON.parse(value)
        const result = z.array(filterStateSchema).safeParse(parsed)

        if (!result.success) return null

        if (validKeys && result.data.some((item) => !validKeys.has(item.accessorKey))) {
          return null
        }

        return result.data
      } catch {
        return null
      }
    },
    serialize: (value) => JSON.stringify(value),
    eq: (a, b) =>
      a.length === b.length &&
      a.every(
        (filter, index) =>
          filter.accessorKey === b[index]?.accessorKey &&
          filter.value === b[index]?.value &&
          filter.variant === b[index]?.variant &&
          filter.operator === b[index]?.operator
      ),
  })
}

/**
 * parser to prisam query whare query
 */
const textVariantParser = (filter: FilterState & { variant: "text" }) => {
  const accessorKey = filter.accessorKey
  const value = filter.value
  const operator = filter.operator

  switch (operator) {
    case "iLike": {
      return { [accessorKey]: { contains: value, mode: "insensitive" } }
    }
    case "notILike": {
      return { NOT: { [accessorKey]: { contains: value, mode: "insensitive" } } }
    }
    case "eq": {
      return { [accessorKey]: value }
    }
    case "ne": {
      return { NOT: { [accessorKey]: value } }
    }
    // case "isEmpty": {
    //   return { [accessorKey]: null }
    // }
    // case "isNotEmpty": {
    //   return { [accessorKey]: { not: null } }
    // }
    default:
      return undefined
  }
}

const numericVariantParser = (filter: FilterState & { variant: "number" }) => {
  const accessorKey = filter.accessorKey
  const value = filter.value
  const operator = filter.operator

  switch (operator) {
    case "eq": {
      return { [accessorKey]: Number(value) }
    }
    case "ne": {
      return { NOT: { [accessorKey]: Number(value) } }
    }
    case "lt": {
      return { [accessorKey]: { lt: Number(value) } }
    }
    case "lte": {
      return { [accessorKey]: { lte: Number(value) } }
    }
    case "gt": {
      return { [accessorKey]: { gt: Number(value) } }
    }
    case "gte": {
      return { [accessorKey]: { gte: Number(value) } }
    }
    // case "isEmpty": {
    //   return { [accessorKey]: null }
    // }
    // case "isNotEmpty": {
    //   return { [accessorKey]: { not: null } }
    // }
    default:
      return undefined
  }
}

const numericRangeVariantParser = (
  filter: FilterState & { variant: "numberRange" }
) => {
  const accessorKey = filter.accessorKey
  const value = filter.value

  return { [accessorKey]: { gt: Number(value[0]), lte: Number(value[1]) } }
}

const dateVariantParser = (filter: FilterState & { variant: "date" }) => {
  const accessorKey = filter.accessorKey
  const value = filter.value
  const operator = filter.operator
  const date = new Date(Number(value))
  const startDay = startOfDay(date)
  const endDay = endOfDay(date)

  switch (operator) {
    case "eq": {
      return { [accessorKey]: { gte: startDay, lte: endDay } }
    }
    case "ne": {
      return { NOT: { [accessorKey]: { gte: startDay, lte: endDay } } }
    }
    case "lt": {
      return { [accessorKey]: { lt: startDay } }
    }
    case "lte": {
      return { [accessorKey]: { lte: endDay } }
    }
    case "gt": {
      return { [accessorKey]: { gt: endDay } }
    }
    case "gte": {
      return { [accessorKey]: { gte: startDay } }
    }
    // case "isEmpty": {
    //   return { [accessorKey]: null }
    // }
    // case "isNotEmpty": {
    //   return { [accessorKey]: { not: null } }
    // }
    default:
      return undefined
  }
}

const dateRangeVariantParser = (
  filter: FilterState & { variant: "dateRange" }
) => {
  const accessorKey = filter.accessorKey
  const value = filter.value
  const min = startOfDay(new Date(Number(value[0])))
  const max = endOfDay(new Date(Number(value[1])))

  return {
    [accessorKey]: { gte: min, lte: max },
  }
}

const booleanVariantParser = (filter: FilterState & { variant: "boolean" }) => {
  const accessorKey = filter.accessorKey
  const value = filter.value
  const operator = filter.operator

  switch (operator) {
    case "eq": {
      return { [accessorKey]: Boolean(value) }
    }
    case "ne": {
      return { NOT: { [accessorKey]: Boolean(value) } }
    }
    default:
      return undefined
  }
}

const selectVariantParser = (filter: FilterState & { variant: "select" }) => {
  const accessorKey = filter.accessorKey
  const value = filter.value
  const operator = filter.operator

  switch (operator) {
    case "eq": {
      return { [accessorKey]: value }
    }
    case "ne": {
      return { NOT: { [accessorKey]: value } }
    }
    // case "isEmpty": {
    //   return { [accessorKey]: null }
    // }
    // case "isNotEmpty": {
    //   return { [accessorKey]: { not: null } }
    // }
    default:
      return undefined
  }
}

const multiSelectVariantParser = (
  filter: FilterState & { variant: "multiSelect" }
) => {
  const accessorKey = filter.accessorKey
  const value = filter.value
  const operator = filter.operator

  switch (operator) {
    case "inArray": {
      return { [accessorKey]: { in: value } }
    }
    case "notInArray": {
      return { NOT: { [accessorKey]: { in: value } } }
    }
    // case "isEmpty": {
    //   return { [accessorKey]: null }
    // }
    // case "isNotEmpty": {
    //   return { [accessorKey]: { not: null } }
    // }
    default:
      return undefined
  }
}

export const getFilterQueryParser = React.cache(
  ({ filters, join }: { filters: FilterState[]; join: FilterJoinState }) => {
    const parsedFilters = filters
      .map((filter) => {
        switch (filter.variant) {
          case "text":
            return textVariantParser(filter)
          case "number":
            return numericVariantParser(filter)
          case "numberRange":
            return numericRangeVariantParser(filter)
          case "date":
            return dateVariantParser(filter)
          case "dateRange":
            return dateRangeVariantParser(filter)
          case "boolean":
            return booleanVariantParser(filter)
          case "select":
            return selectVariantParser(filter)
          case "multiSelect":
            return multiSelectVariantParser(filter)
          default:
            return undefined
        }
      })
      .filter(Boolean)

    return parsedFilters.length > 0 ? { [join]: parsedFilters } : {}
  }
)

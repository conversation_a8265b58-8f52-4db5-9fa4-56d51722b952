"use client"

import * as React from "react"
import dynamic from "next/dynamic"
import { formatDate } from "date-fns"
import {
  CheckIcon,
  ChevronDown,
  ChevronsUpDownIcon,
  GripVertical,
  ListFilter,
  SearchIcon,
  Trash2,
} from "lucide-react"
import { ar } from "react-day-picker/locale"
import { toast } from "sonner"

import { DynamicObject } from "@/types/globals"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Faceted,
  FacetedBadgeList,
  FacetedContent,
  FacetedEmpty,
  FacetedGroup,
  FacetedInput,
  FacetedItem,
  FacetedList,
  FacetedTrigger,
} from "@/components/ui/faceted"
import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Sortable,
  SortableContent,
  SortableItem,
  SortableItemHandle,
} from "@/components/ui/sortable"

import { FilterContext } from "./contexts"
import { useFilterState } from "./hooks"
import { getDefaultFilterOperator, getFilterOperators } from "./lib"
import {
  FilterItem,
  FilterJoinState,
  FilterState,
  Operator,
  ToolbarFilterProps,
} from "./types"

export function ToolbarFilter<T extends DynamicObject = DynamicObject>({
  items,
  shallow = false,
  debounceMs = 300,
  className,
}: ToolbarFilterProps<T>) {
  return (
    <FilterContext
      value={{
        items,
        shallow,
        debounceMs,
      }}
    >
      <Filter className={className} />
    </FilterContext>
  )
}

//
// --------------------------------------------------------------------------------------
// Filter
// --------------------------------------------------------------------------------------
//

// المكون الرئيسي الذي يحتوي على جميع مكونات وعناصر الفلترات
// تم فصلة الى مكون منفصل لكي يتمكن من استخدام خطاف الفلتر
function Filter({ className }: { className?: string }) {
  const id = React.useId()
  const labelId = React.useId()
  const descriptionId = React.useId()
  const { filters, setFilters, addFilter, resetFilters, dictionaries, dir } =
    useFilterState()

  return (
    <Sortable
      value={filters}
      onValueChange={setFilters}
      getItemValue={(item: FilterState) => item.filterId}
    >
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" size="sm" className={className}>
            <ListFilter />
            {dictionaries.triggerButtonLabel}
            {filters.length > 0 && (
              <Badge
                variant="secondary"
                className="h-[18.24px] rounded-[3.2px] px-[5.12px] font-mono text-[10.4px] font-normal"
              >
                {filters.length}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          dir={dir}
          align="start"
          aria-describedby={descriptionId}
          aria-labelledby={labelId}
          className="flex w-full max-w-[var(--radix-popover-content-available-width)] origin-[var(--radix-popover-content-transform-origin)] flex-col gap-3.5 p-4 sm:min-w-[380px]"
        >
          <div className="flex flex-col gap-1">
            <h4 id={labelId} className="leading-none font-medium">
              {filters.length > 0
                ? dictionaries.popover.title.withFilters
                : dictionaries.popover.title.noFilters}
            </h4>
            <p
              id={descriptionId}
              className={cn(
                "text-muted-foreground text-sm",
                filters.length > 0 && "sr-only"
              )}
            >
              {filters.length > 0
                ? dictionaries.popover.description.withFilters
                : dictionaries.popover.description.noFilters}
            </p>
          </div>
          {filters.length > 0 ? (
            <SortableContent dir={dir} asChild>
              <div
                role="list"
                className="flex max-h-[300px] flex-col gap-2 overflow-y-auto p-1"
              >
                {filters.length > 0 &&
                  filters.map((filter, index) => (
                    <DynamicFilterItem
                      index={index}
                      key={filter.filterId}
                      filter={filter as FilterState}
                      filterItemId={`${id}-filter-${filter.filterId}`}
                    />
                  ))}
              </div>
            </SortableContent>
          ) : null}
          <div className="flex w-full items-center gap-2">
            <Button size="sm" className="rounded" onClick={addFilter}>
              {dictionaries.popover.buttonLabels.addFilter}
            </Button>
            {filters.length > 0 ? (
              <Button
                variant="outline"
                size="sm"
                className="rounded"
                onClick={resetFilters}
              >
                {dictionaries.popover.buttonLabels.resetFilters}
              </Button>
            ) : null}
          </div>
        </PopoverContent>
      </Popover>
    </Sortable>
  )
}

//
// --------------------------------------------------------------------------------------
// Filter Item
// --------------------------------------------------------------------------------------
//

// جلب المكون بشكل ديناميكي لتسريع التحميل الأولي
const DynamicFilterItem = dynamic(async () => FilterMenuItem, {
  ssr: false,
  loading: () => <FilterMenuItemSkeleton />,
})

// المكون الرئيسي الذي يعرض كل الحقول (4 حقول) الخاصة بعنصر واحد من قائمة التصفية
function FilterMenuItem({
  index,
  filter,
  filterItemId,
}: {
  index: number
  filterItemId: string
  filter: FilterState
}) {
  const { items, dir, removeFilter } = useFilterState()

  const item = items.find((item) => item.accessorKey === filter.accessorKey)
  if (!item) return null

  return (
    <SortableItem dir={dir} value={filter.filterId} asChild>
      <div
        role="listitem"
        id={filterItemId}
        tabIndex={-1}
        className="flex items-center gap-2"
      >
        <FilterMenuItemJoinSelector index={index} filterItemId={filterItemId} />
        <FilterMenuItemSelector
          filterItemId={filterItemId}
          filter={filter}
          item={item}
        />
        <FilterMenuItemOperatorSelector
          filter={filter}
          filterItemId={filterItemId}
        />
        <div className="min-w-36 flex-1">
          <FilterMenuItemValueInput
            filterItemId={filterItemId}
            filter={filter}
            item={item}
          />
        </div>
        <Button
          aria-controls={filterItemId}
          variant="outline"
          size="icon"
          className="size-8 rounded"
          onClick={() => removeFilter(filter.filterId)}
        >
          <Trash2 />
        </Button>
        <SortableItemHandle asChild>
          <Button variant="outline" size="icon" className="size-8 rounded">
            <GripVertical />
          </Button>
        </SortableItemHandle>
      </div>
    </SortableItem>
  )
}

// هذا المكون يعرض بشكل مؤقت أثناء تحميل المكون الفعلي
function FilterMenuItemSkeleton() {
  return (
    <div className="flex items-center gap-2">
      <Skeleton className="h-8 min-w-[72px] rounded" />
      <Skeleton className="h-8 w-32 rounded" />
      <Skeleton className="h-8 w-32 rounded" />
      <Skeleton className="h-8 min-w-36 flex-1 rounded" />
      <Skeleton className="size-8 shrink-0 rounded" />
      <Skeleton className="size-8 shrink-0 rounded" />
    </div>
  )
}

//
// --------------------------------------------------------------------------------------
// Filter Item Join Selector
// --------------------------------------------------------------------------------------
//

// مكون يعرض حقل خيارات لتحديد طريقة الربط بين الفلترات AND / OR
function FilterMenuItemJoinSelector({
  index,
  filterItemId,
}: {
  index: number
  filterItemId: string
}) {
  const { joinOperator, dictionaries, dir, setJoinOperator } = useFilterState()

  const dict = dictionaries.filterItem.JoinSelector
  const joinOperatorListboxId = `${filterItemId}-join-operator-listbox`

  return (
    <div className="w-19 text-center">
      {index === 0 ? (
        <span className="text-muted-foreground text-sm">{dict.where}</span>
      ) : index === 1 ? (
        <Select
          dir={dir}
          value={joinOperator}
          onValueChange={(value: FilterJoinState) => setJoinOperator(value)}
        >
          <SelectTrigger
            aria-label="Select join operator"
            aria-controls={joinOperatorListboxId}
            className="h-8 w-full rounded lowercase [&[data-size]]:h-8"
          >
            <SelectValue placeholder={dict?.[joinOperator]} />
          </SelectTrigger>
          <SelectContent
            id={joinOperatorListboxId}
            position="popper"
            className="min-w-(--radix-select-trigger-width) lowercase"
          >
            <SelectItem value={"AND"}>{dict.AND}</SelectItem>
            <SelectItem value={"OR"}>{dict.OR}</SelectItem>
          </SelectContent>
        </Select>
      ) : (
        <span className="text-muted-foreground w-full text-sm lowercase">
          {dict?.[joinOperator]}
        </span>
      )}
    </div>
  )
}

//
// --------------------------------------------------------------------------------------
// Filter Item Selector
// --------------------------------------------------------------------------------------
//

// مكون يعرض حقل خيارات لتحديد العنصر الذي تريد تطبيق الفلتر عليه
function FilterMenuItemSelector({
  filterItemId,
  filter,
  item,
}: {
  filter: FilterState
  filterItemId: string
  item: FilterItem
}) {
  const { items, dictionaries, dir, updateFilter } = useFilterState()
  const [open, setOpen] = React.useState(false)

  const dict = dictionaries.filterItem.itemSelector
  const fieldListboxId = `${filterItemId}-field-listbox`

  const onItemSelect = React.useCallback(
    (value: string, item: FilterItem) => {
      updateFilter(filter.filterId, {
        accessorKey: value,
        variant: item.variant,
        operator: getDefaultFilterOperator(item.variant),
        value: "",
      })

      setOpen(false)
    },
    [filter.filterId, updateFilter]
  )

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          role="combobox"
          aria-controls={fieldListboxId}
          variant="outline"
          size="sm"
          className="w-32 justify-between rounded font-normal"
        >
          <span className="truncate">{item.label}</span>
          <ChevronsUpDownIcon className="opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        id={fieldListboxId}
        align="start"
        className="w-40 origin-[var(--radix-popover-content-transform-origin)] p-0"
      >
        <Command dir={dir}>
          <CommandInput placeholder={dict.searchPlaceholder} />
          <CommandList>
            <CommandEmpty>{dict.noFieldsFound}</CommandEmpty>
            <CommandGroup>
              {items.map((item) => (
                <CommandItem
                  key={item.accessorKey}
                  value={item.accessorKey}
                  onSelect={(value) => onItemSelect(value, item)}
                >
                  <span className="truncate">{item.label}</span>
                  <CheckIcon
                    className={cn(
                      "ms-auto",
                      item.accessorKey === filter.accessorKey ? "opacity-100" : "opacity-0"
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}

//
// --------------------------------------------------------------------------------------
// Filter Item Operator Selector
// --------------------------------------------------------------------------------------
//

// مكون يعرض حقل خيارات لتحديد نوع شرط الفلتر
function FilterMenuItemOperatorSelector({
  filter,
  filterItemId,
}: {
  filter: FilterState
  filterItemId: string
}) {
  const { dir, dictionaries, updateFilter } = useFilterState()

  const dict = dictionaries.filterItem.operatorSelector
  const operatorListboxId = `${filterItemId}-operator-listbox`

  const filterOperators = React.useMemo(
    () => getFilterOperators(filter.variant, dict.operators),
    [dict.operators, filter.variant]
  )

  // وجود عدة شروط في هذه الدالة هو لضمان
  // تغيير نوع الفلتر من رقم إلى نطاق رقم أو تاريخ إلى نطاق تاريخ
  // عندما يتم تحديد شرط "بين" لأنها غير مدرجة ضمن خيارات انواع الفلتر
  const onOperatorChange = React.useCallback(
    (operator: Operator) => {
      if (filter.variant === "number" || filter.variant === "numberRange") {
        if (operator === "isBetween") {
          updateFilter(filter.filterId, {
            operator: "isBetween",
            variant: "numberRange",
            value: ["", ""],
          })
        } else {
          updateFilter(filter.filterId, {
            operator: operator,
            variant: "number",
            value: Array.isArray(filter.value) ? filter.value[0] : filter.value,
          })
        }
        return
      }

      if (filter.variant === "date" || filter.variant === "dateRange") {
        if (operator === "isBetween") {
          updateFilter(filter.filterId, {
            operator: "isBetween",
            variant: "dateRange",
            value: ["", ""],
          })
        } else {
          updateFilter(filter.filterId, {
            operator: operator,
            variant: "date",
            value: Array.isArray(filter.value) ? filter.value[0] : filter.value,
          })
        }
        return
      }

      if (operator === "isEmpty" || operator === "isNotEmpty") {
        updateFilter(filter.filterId, {
          operator: operator,
          value: operator === "isEmpty" ? "empty" : "notEmpty",
        })
        return
      }

      updateFilter(filter.filterId, {
        operator: operator,
      })
    },
    [filter.filterId, filter.value, filter.variant, updateFilter]
  )

  return (
    <Select dir={dir} value={filter.operator} onValueChange={onOperatorChange}>
      <SelectTrigger
        aria-controls={operatorListboxId}
        className="h-8 w-32 rounded lowercase [&[data-size]]:h-8"
      >
        <div className="truncate">
          <SelectValue placeholder={filter.operator} />
        </div>
      </SelectTrigger>
      <SelectContent
        id={operatorListboxId}
        className="origin-[var(--radix-select-content-transform-origin)]"
      >
        {filterOperators.map((operator) => (
          <SelectItem
            key={operator.value}
            value={operator.value}
            className="lowercase"
          >
            {operator.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}

//
// --------------------------------------------------------------------------------------
// Filter Item Value Input
// --------------------------------------------------------------------------------------
//

// مكون يحدد الحقل المناسب التي يجب أن يعرضه بناءً على نوع او وشرط الفلتر
function FilterMenuItemValueInput({
  filter,
  filterItemId,
  item,
}: {
  filter: FilterState
  item: FilterItem
  filterItemId: string
}) {
  const inputId = `${filterItemId}-input`

  if (filter.operator === "isEmpty" || filter.operator === "isNotEmpty") {
    return (
      <FilterMenuItemValueInputEmptyOperator
        inputId={inputId}
        filter={filter}
        item={item}
      />
    )
  }

  if (filter.variant === "text" && item.variant === "text") {
    return (
      <FilterMenuItemValueInputTextOperator
        inputId={inputId}
        filter={filter}
        item={item}
      />
    )
  }

  if (filter.variant === "number" && item.variant === "number") {
    return (
      <FilterMenuItemValueInputNumberOperator
        inputId={inputId}
        filter={filter}
        item={item}
      />
    )
  }

  if (filter.variant === "numberRange" && item.variant === "number") {
    return (
      <FilterMenuItemValueInputNumberRangeOperator
        inputId={inputId}
        filter={filter}
        item={item}
      />
    )
  }

  if (
    (filter.variant === "select" && item.variant === "select") ||
    (filter.variant === "multiSelect" && item.variant === "multiSelect")
  ) {
    return (
      <FilterMenuItemValueInputSelectOperator
        inputId={inputId}
        filter={filter}
        item={item}
      />
    )
  }

  if (filter.variant === "boolean" && item.variant === "boolean") {
    return (
      <FilterMenuItemValueInputBooleanOperator
        inputId={inputId}
        filter={filter}
        item={item}
      />
    )
  }

  if (filter.variant === "date" && item.variant === "date") {
    return (
      <FilterMenuItemValueInputDateOperator
        inputId={inputId}
        filter={filter}
        item={item}
      />
    )
  }

  if (filter.variant === "dateRange" && item.variant === "date") {
    return (
      <FilterMenuItemValueInputDateRangeOperator
        inputId={inputId}
        filter={filter}
        item={item}
      />
    )
  }
}

//
// --------------------------------------------------------------------------------------
// Filter Item Value Input Empty Operator
// --------------------------------------------------------------------------------------
//

// مكون يعرض حقل وهمي عندما يكون نوع الـ operator isEmpty أو isNotEmpty
function FilterMenuItemValueInputEmptyOperator({
  inputId,
  filter,
  item,
}: {
  inputId: string
  item: FilterItem
  filter: FilterState
}) {
  return (
    <div
      id={inputId}
      role="status"
      aria-label={`${item?.label} filter is ${
        filter.operator === "isEmpty" ? "empty" : "not empty"
      }`}
      aria-live="polite"
      className="dark:bg-input/30 h-8 w-full rounded border bg-transparent"
    />
  )
}

//
// --------------------------------------------------------------------------------------
// Filter Item Value Input Text Operator
// --------------------------------------------------------------------------------------
//

// مكون يعرض حقل ادخال نص عندما يكون نوع الـ operator text
function FilterMenuItemValueInputTextOperator({
  inputId,
  filter,
  item,
}: {
  inputId: string
  item: FilterItem & { variant: "text" }
  filter: FilterState & { variant: "text" }
}) {
  const { updateFilter } = useFilterState()

  return (
    <div className={cn("relative w-full")}>
      <Input
        id={inputId}
        type={"search"}
        aria-label={`${item?.label} filter value`}
        aria-describedby={`${inputId}-description`}
        inputMode={"search"}
        placeholder={item?.placeholder}
        className={cn(
          // base
          "h-8 max-h-full w-full rounded-sm ps-8",
          // hide cancel button and decoration
          "[&::-webkit-search-cancel-button]:hidden [&::-webkit-search-decoration]:hidden"
        )}
        defaultValue={
          typeof filter.value === "string" ? filter.value : undefined
        }
        onChange={(event) =>
          updateFilter(filter.filterId, {
            value: event.target.value,
          })
        }
      />
      <div
        className={cn(
          // base
          "pointer-events-none absolute start-2 bottom-0 flex h-full items-center justify-center",
          // text color
          "text-muted-foreground/60"
        )}
      >
        <SearchIcon className="size-5 shrink-0" aria-hidden="true" />
      </div>
    </div>
  )
}

//
// --------------------------------------------------------------------------------------
// Filter Item Value Input Number Operator
// --------------------------------------------------------------------------------------
//

// مكون يعرض حقل ادخال رقم عندما يكون نوع الـ operator number
function FilterMenuItemValueInputNumberOperator({
  inputId,
  filter,
  item,
}: {
  inputId: string
  item: FilterItem & { variant: "number" }
  filter: FilterState & { variant: "number" }
}) {
  const { updateFilter } = useFilterState()

  const rangeMin = item.range?.min
  const rangeMax = item.range?.max

  const enableChange = React.useCallback(
    (value: string) => {
      const val = Number(value)

      if (rangeMin !== undefined && val < rangeMin) {
        toast.error(`The minimum value must be greater than ${rangeMin}`)
        return false
      }
      if (rangeMax !== undefined && val > rangeMax) {
        toast.error(`The maximum value must be less than ${rangeMax}`)
        return false
      }

      return true
    },
    [rangeMax, rangeMin]
  )

  const onValueChange = React.useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = event.target.value

      if (!enableChange(value)) return

      updateFilter(filter.filterId, {
        value: value,
      })
    },
    [enableChange, filter.filterId, updateFilter]
  )

  switch (filter.operator) {
    default:
      return (
        <div className="relative w-full">
          <Input
            id={inputId}
            type={"number"}
            aria-label={`${item?.label} filter value`}
            aria-describedby={`${inputId}-description`}
            aria-valuemin={rangeMin}
            aria-valuemax={rangeMax}
            min={rangeMin}
            max={rangeMax}
            step={item.step ?? 1}
            inputMode={"numeric"}
            placeholder={item.placeholder}
            className={cn(
              "h-8 [&>input]:w-full [&>input]:rounded-sm",
              item.unit && "[&>input]:pr-8"
            )}
            defaultValue={filter.value ?? ""}
            onChange={onValueChange}
          />
          {item?.unit && (
            <span className="bg-accent text-muted-foreground absolute top-px right-px bottom-px flex items-center rounded-r-sm px-2 text-sm">
              {item.unit}
            </span>
          )}
        </div>
      )
  }
}

//
// --------------------------------------------------------------------------------------
// Filter Item Value Input Number Range Operator
// --------------------------------------------------------------------------------------
//

// مكون يعرض حقلين ادخال رقمي الأول للقيمة الصغر والثاني للقيمة الأكبر عندما يكون نوع الـ operator numberRange
function FilterMenuItemValueInputNumberRangeOperator({
  inputId,
  filter,
  item,
}: {
  inputId: string
  item: FilterItem & { variant: "number" }
  filter: FilterState & { variant: "numberRange" }
}) {
  const { updateFilter } = useFilterState()
  const range = item?.range

  const rangeMin = range?.min
  const rangeMax = range?.max

  const enableChange = React.useCallback(
    (value: string) => {
      const val = Number(value)

      if (rangeMin !== undefined && val < rangeMin) {
        toast.error(`The minimum value must be greater than ${rangeMin}`)
        return false
      }
      if (rangeMax !== undefined && val > rangeMax) {
        toast.error(`The maximum value must be less than ${rangeMax}`)
        return false
      }

      return true
    },
    [rangeMax, rangeMin]
  )

  const onValueChange = React.useCallback(
    (event: React.ChangeEvent<HTMLInputElement>, from: "min" | "max") => {
      const value = event.target.value

      if (!enableChange(value)) return

      if (from === "min") {
        updateFilter(filter.filterId, {
          value: [value, filter.value[0]],
        })
        return
      }

      if (from === "max") {
        updateFilter(filter.filterId, {
          value: [filter.value[0] ?? "", value],
        })
        return
      }
    },
    [enableChange, filter.filterId, filter.value, updateFilter]
  )

  return (
    <div data-slot="range" className={cn("flex w-full items-center gap-2")}>
      <Input
        id={`${inputId}-min`}
        type="number"
        aria-label={`${item?.label} minimum value`}
        aria-valuemin={rangeMin}
        aria-valuemax={rangeMax}
        data-slot="range-min"
        inputMode="numeric"
        placeholder={rangeMin?.toString()}
        min={rangeMin}
        max={rangeMax}
        step={item.step ?? 1}
        className="h-8 w-full rounded"
        defaultValue={filter.value[0] ?? ""}
        onChange={(event) => onValueChange(event, "min")}
      />
      <span className="text-muted-foreground sr-only shrink-0">to</span>
      <Input
        id={`${inputId}-max`}
        type="number"
        aria-label={`${item?.label} maximum value`}
        aria-valuemin={rangeMin}
        aria-valuemax={rangeMax}
        data-slot="range-max"
        inputMode="numeric"
        placeholder={rangeMax?.toString()}
        min={rangeMin}
        max={rangeMax}
        step={item.step ?? 1}
        className="h-8 w-full rounded"
        defaultValue={filter.value[1] ?? ""}
        onChange={(event) => onValueChange(event, "max")}
      />
    </div>
  )
}

//
// --------------------------------------------------------------------------------------
// Filter Item Value Input Select Operator
// --------------------------------------------------------------------------------------
//

// مكون يعرض حقل اختيار قيمة واحدة او اكثر من بين قائمة محددة مسبقًا عندما يكون نوع الـ operator select أو multiSelect
function FilterMenuItemValueInputSelectOperator({
  inputId,
  filter,
  item,
}: {
  inputId: string
  item: FilterItem & { variant: "select" | "multiSelect" }
  filter: FilterState & { variant: "select" | "multiSelect" }
}) {
  const { updateFilter } = useFilterState()
  const [open, setIsOpen] = React.useState(false)

  const inputListboxId = `${inputId}-listbox`
  const multiple = filter.variant === "multiSelect"

  const selectedValues = multiple
    ? Array.isArray(filter.value)
      ? filter.value
      : []
    : typeof filter.value === "string"
      ? filter.value
      : undefined

  return (
    <Faceted
      open={open}
      onOpenChange={setIsOpen}
      value={selectedValues}
      onValueChange={(value) => {
        updateFilter(filter.filterId, {
          value,
        })
      }}
      multiple={multiple}
    >
      <FacetedTrigger asChild>
        <Button
          id={inputId}
          aria-controls={inputListboxId}
          aria-label={`${item?.label} filter value${multiple ? "s" : ""}`}
          variant="outline"
          size="sm"
          className="w-full rounded font-normal"
        >
          <FacetedBadgeList
            options={item?.options}
            placeholder={
              item?.placeholder ?? `Select option${multiple ? "s" : ""}...`
            }
          />
        </Button>
      </FacetedTrigger>
      <FacetedContent
        id={inputListboxId}
        className="w-[200px] origin-[var(--radix-popover-content-transform-origin)]"
      >
        <FacetedInput
          aria-label={`Search ${item?.label} options`}
          placeholder={item?.placeholder ?? "Search options..."}
        />
        <FacetedList>
          <FacetedEmpty>No options found.</FacetedEmpty>
          <FacetedGroup>
            {item?.options?.map((option) => (
              <FacetedItem
                className="gap-3"
                key={option.value}
                value={option.value}
              >
                {option.icon && (
                  <option.icon className="text-muted-foreground size-4" />
                )}
                <span>{option.label}</span>
                {option.count && (
                  <Badge
                    variant="outline"
                    className="ms-auto flex h-full items-center justify-center rounded-sm px-1 py-px font-mono text-xs"
                  >
                    {option.count}
                  </Badge>
                )}
              </FacetedItem>
            ))}
          </FacetedGroup>
        </FacetedList>
      </FacetedContent>
    </Faceted>
  )
}

//
// --------------------------------------------------------------------------------------
// Filter Item Value Input Boolean Operator
// --------------------------------------------------------------------------------------
//

// مكون يعرض حقل خيارات الخيار الأول (صحيح) والخيار الثاني (خطأ) عندما يكون نوع الـ operator boolean
function FilterMenuItemValueInputBooleanOperator({
  inputId,
  filter,
  item,
}: {
  inputId: string
  item: FilterItem & { variant: "boolean" }
  filter: FilterState & { variant: "boolean" }
}) {
  const { updateFilter } = useFilterState()

  return (
    <Select
      value={String(filter.value)}
      onValueChange={(value) =>
        updateFilter(filter.filterId, {
          value,
        })
      }
    >
      <SelectTrigger
        id={inputId}
        aria-controls={`${inputId}-listbox`}
        aria-label={`${item?.label} boolean filter`}
        className="h-8 w-full rounded [&[data-size]]:h-8"
      >
        <SelectValue placeholder={"Select option..."} />
      </SelectTrigger>
      <SelectContent id={`${inputId}-listbox`}>
        <SelectItem value="true">True</SelectItem>
        <SelectItem value="false">False</SelectItem>
      </SelectContent>
    </Select>
  )
}

//
// --------------------------------------------------------------------------------------
// Filter Item Value Input Date Operator
// --------------------------------------------------------------------------------------
//

// مكون يعرض زر التاريخ عند النقر على الزر يتم عرض تقويم لإختيار التاريخ عندما يكون نوع الـ operator date
function FilterMenuItemValueInputDateOperator({
  inputId,
  filter,
  item,
}: {
  inputId: string
  item: FilterItem & { variant: "date" }
  filter: FilterState & { variant: "date" }
}) {
  const { updateFilter } = useFilterState()
  const range = item?.range

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          size="sm"
          variant="outline"
          aria-label={`${item?.label} date filter`}
          aria-controls={`${inputId}-listbox`}
          className="w-full justify-between rounded font-normal"
        >
          {filter.value
            ? formatDate(new Date(Number(filter.value)), "yyyy-MM-dd")
            : "Select date"}
          <ChevronDown />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        id={`${inputId}-listbox`}
        className="w-auto overflow-hidden p-0"
        align="start"
      >
        <Calendar
          dir="rtl"
          locale={ar}
          mode="single"
          disabled={[
            ...(range?.min ? [{ before: new Date(range.min) }] : []),
            ...(range?.max ? [{ after: new Date(range.max) }] : []),
          ]}
          selected={new Date(Number(filter.value))}
          captionLayout="dropdown"
          onSelect={(date) => {
            updateFilter(filter.filterId, {
              value: date?.getTime().toString() ?? "",
            })
          }}
        />
      </PopoverContent>
    </Popover>
  )
}

//
// --------------------------------------------------------------------------------------
// Filter Item Value Input Date Range Operator
// --------------------------------------------------------------------------------------
//

// مكون يعرض زر التاريخ عند النقر على الزر يتم عرض تقويم لإختيار تاريخين الأول للقيمة الصغر والثاني للقيمة الأكبر عندما يكون نوع الـ operator dateRange
function FilterMenuItemValueInputDateRangeOperator({
  inputId,
  filter,
  item,
}: {
  inputId: string
  item: FilterItem & { variant: "date" }
  filter: FilterState & { variant: "dateRange"; operator: "isBetween" }
}) {
  const { updateFilter } = useFilterState()

  const from = filter.value[0] ? new Date(Number(filter.value[0])) : undefined
  const to = filter.value[1] ? new Date(Number(filter.value[1])) : undefined

  const range = item?.range

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          size="sm"
          variant="outline"
          aria-label={`${item?.label} date range filter`}
          aria-controls={`${inputId}-listbox`}
          className="w-full justify-between rounded font-normal"
        >
          {
            <span className="truncate">
              {from && to
                ? `From -> ${formatDate(from, "yyyy/MM/dd")} - To -> ${formatDate(to, "yyyy/MM/dd")}`
                : "Pick a date"}
            </span>
          }
          <ChevronDown />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        id={`${inputId}-listbox`}
        className="w-auto overflow-hidden p-0"
        align="start"
      >
        <Calendar
          disabled={[
            ...(range?.min ? [{ before: new Date(range.min) }] : []),
            ...(range?.max ? [{ after: new Date(range.max) }] : []),
          ]}
          mode="range"
          selected={{
            from: from ?? new Date(),
            to: to ?? new Date(),
          }}
          captionLayout="dropdown"
          onSelect={(date) => {
            updateFilter(filter.filterId, {
              value: [
                (date?.from?.getTime() ?? "").toString(),
                (date?.to?.getTime() ?? "").toString(),
              ],
            })
          }}
        />
      </PopoverContent>
    </Popover>
  )
}

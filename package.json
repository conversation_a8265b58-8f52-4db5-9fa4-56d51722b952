{"name": "quick-dashboard", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "registry:build": "shadcn build", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:seed": "node src/db/seed.js", "db:studio": "prisma studio", "db:reset": "prisma migrate reset --force"}, "dependencies": {"@diceui/tags-input": "^0.7.2", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@faker-js/faker": "^9.8.0", "@floating-ui/react": "^0.27.8", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "filepond": "^4.32.7", "filepond-plugin-file-validate-type": "^1.2.9", "lucide-react": "^0.511.0", "motion": "^12.12.2", "nanoid": "^5.1.5", "next": "15.3.2", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "react": "^19.0.0", "react-day-picker": "^9.8.0", "react-dom": "^19.0.0", "react-filepond": "^7.1.3", "react-hook-form": "^7.56.4", "react-phone-number-input": "^3.4.12", "shadcn": "^2.9.0-canary.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.0", "zod": "^3.25.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@prisma/client": "^6.8.2", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "eslint-config-prettier": "^10.1.5", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "prisma": "^6.8.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.0", "typescript": "^5"}}
"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/db/generated"
import type { <PERSON>umn, ColumnDef } from "@tanstack/react-table"
import { format } from "date-fns"
import {
  Calendar,
  CheckCircle,
  CheckCircle2,
  MoreHorizontal,
  Text,
  Trash2,
  XCircle,
} from "lucide-react"

import { cn } from "@/lib/utils"
import { useDataTable } from "@/hooks/use-data-table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { DataTable } from "@/components/data-table/data-table"
import {
  DataTableActionBar,
  DataTableActionBarAction,
} from "@/components/data-table/data-table-action-bar"
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/toolbar"

export default function DataTableDemo(props: { data: Promise<Admin[]> }) {
  const [isPending, startTransition] = React.useTransition()
  const data = React.use(props.data)

  const columns = React.useMemo<ColumnDef<Admin>[]>(
    () => [
      {
        id: "select",
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) =>
              table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
          />
        ),
        size: 32,
        enableSorting: false,
        enableHiding: false,
      },
      {
        id: "name",
        accessorKey: "name",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="الاسم" />
        ),
        meta: {
          label: "الأسم",
          placeholder: "بحث بالإسم...",
          variant: "text",
          icon: Text,
        },
        enableColumnFilter: true,
      },
      {
        id: "isActive",
        accessorKey: "isActive",
        header: ({ column }: { column: Column<Admin, unknown> }) => (
          <DataTableColumnHeader column={column} title="حالة الحساب" />
        ),
        cell: ({ cell }) => {
          const isActive = cell.getValue<Admin["isActive"]>()
          const Icon = isActive ? CheckCircle2 : XCircle

          return (
            <Badge variant="outline" className="capitalize">
              <Icon />
              {isActive ? "مفعل" : "معطل"}
            </Badge>
          )
        },
        meta: {
          label: "حالة الحساب",
          variant: "boolean",
          options: [
            { label: "مفعل", value: "true", icon: CheckCircle },
            { label: "معطل", value: "false", icon: XCircle },
          ],
        },
        enableColumnFilter: true,
      },
      {
        id: "createdAt",
        accessorKey: "createdAt",
        header: ({ column }: { column: Column<Admin, unknown> }) => (
          <DataTableColumnHeader column={column} title="تاريخ الإنشاء" />
        ),
        cell: ({ getValue }) => (
          <div className="flex items-center gap-x-2">
            <Calendar className="text-foreground size-3.5" />
            {format(getValue() as Date, "yyyy/MM/dd")}
          </div>
        ),
        meta: {
          label: "تاريخ الإنشاء",
          variant: "date",
          placeholder: "تاريخ الإنشاء...",
          icon: Calendar,
        },
        enableColumnFilter: true,
      },
      {
        id: "actions",
        cell: function Cell() {
          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>Edit</DropdownMenuItem>
                <DropdownMenuItem variant="destructive">
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )
        },
        size: 32,
      },
    ],
    []
  )

  const { table } = useDataTable({
    data,
    shallow: false,
    columns,
    pageCount: 1,
    enableAdvancedFilter: false,
    initialState: {
      columnPinning: { right: ["actions"] },
    },
    getRowId: (row) => row.id,
  })

  return (
    <div className="data-table-container">
      <Toolbar startTransition={startTransition}>
        <ToolbarFilter<Admin>
          items={[
            {
              variant: "text",
              field: "name",
              label: "الاسم",
              placeholder: "بحث بالإسم...",
            },
          ]}
        />
        <ToolbarSearch item={{ field: "name", placeholder: "بحث بالإسم..." }} />
        <ToolbarSorting
          items={[
            {
              field: "name",
              label: "الاسم",
            },
            {
              field: "createdAt",
              label: "تاريخ الإنشاء",
            },
          ]}
        />
      </Toolbar>
      <DataTable
        className={cn(isPending && "opacity-50")}
        table={table}
        actionBar={
          <DataTableActionBar table={table}>
            {/* Add your custom actions here */}

            <DataTableActionBarAction
              size="icon"
              tooltip="Delete"
              onClick={() => {
                console.log("delete")
              }}
            >
              <Trash2 />
            </DataTableActionBarAction>
          </DataTableActionBar>
        }
      />
    </div>
  )
}

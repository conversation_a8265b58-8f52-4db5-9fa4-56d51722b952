import { createParser } from "nuqs/server"
import { z } from "zod"

export const searchStateSchema = z.union([
  z.object({
    field: z.string().min(1),
    value: z.string().min(1),
  }),
  z.object({
    field: z.undefined(),
    value: z.undefined(),
  }),
])

export type SearchState = z.infer<typeof searchStateSchema>

//
// --------------------------------------------------------------------------------------
// Search Parser
// --------------------------------------------------------------------------------------
//

export const getSearchStateParser = (itemKey?: string) => {
  return createParser({
    parse: (value) => {
      try {
        const parsed = JSON.parse(value)
        const result = searchStateSchema.safeParse(parsed)

        if (!result.success) return null

        if (itemKey && result.data?.field !== itemKey) {
          return null
        }

        return result.data
      } catch {
        return null
      }
    },
    serialize: (value) => JSON.stringify(value),
    eq: (a, b) => a.field === b.field && a.value === b.value,
  })
}

export const getSearchQueryParser = (search: SearchState) => {
  if (!search.field || !search.value) return {} // no search value or field provided, return null to skip search query parsing.
  return { [search.field]: { contains: search.value, mode: "insensitive" } }
}

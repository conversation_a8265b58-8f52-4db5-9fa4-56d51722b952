import { z } from "zod/v4"

import { DynamicObject } from "@/types/globals"

import type { sortingStateSchema } from "../lib"

export type SortingState = z.infer<typeof sortingStateSchema>
export type SortDirection = SortingState["value"] // "asc" | "desc"

type SortingItem<T extends DynamicObject = DynamicObject> = {
  /** مفتاح كائن البيانات الذي تريد تمكين استخدامه للفرز */
  accessorKey: Extract<keyof T, string>
  /** العنوان الذي سيظهر في القائمة */
  label: string
}

export type OnSortRemove = (sortId: string) => void
export type OnSortUpdate = (
  sortId: string,
  updates: Partial<SortingState>
) => void

export interface SortingItemProps {
  sort: SortingState
  sortItemId: string
  items: { accessorKey: string; label: string }[]
  updateSort: OnSortUpdate
  removeSort: OnSortRemove
}

export type ToolbarSortingProps<T extends DynamicObject = DynamicObject> = {
  /**
   * مصفوفة العناصر التي تريد تمكين استخدامها للفرز.
   */
  items: SortingItem<T>[]
  className?: string
}
